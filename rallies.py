import sys
import yfinance as yf
import pandas as pd
import numpy as np
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QHBoxLayout,
                             QWidget, QPushButton, QLineEdit, QLabel, QMessageBox)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QPalette, QColor
import pyqtgraph as pg

class DataFetcher(QThread):
    """Thread for fetching options data without blocking the GUI"""
    data_ready = pyqtSignal(pd.DataFrame)
    error_occurred = pyqtSignal(str)

    def __init__(self, ticker):
        super().__init__()
        self.ticker = ticker

    def run(self):
        try:
            df = self.fetch_net_premiums(self.ticker)
            self.data_ready.emit(df)
        except Exception as e:
            self.error_occurred.emit(str(e))

    def fetch_net_premiums(self, ticker):
        """Fetch net premiums data for the given ticker"""
        # Download option chain expirations
        stock = yf.Ticker(ticker)
        exps = stock.options
        data = []

        # Loop through expirations
        for exp in exps:
            opt = stock.option_chain(exp)
            calls = opt.calls.copy()
            puts = opt.puts.copy()

            # Handle NaN values in volume and lastPrice
            calls['volume'] = calls['volume'].fillna(0)
            puts['volume'] = puts['volume'].fillna(0)
            calls['lastPrice'] = calls['lastPrice'].fillna(0)
            puts['lastPrice'] = puts['lastPrice'].fillna(0)

            # Compute total premium = lastPrice * volume for calls and puts
            calls['premium_value'] = calls['lastPrice'] * calls['volume']
            puts['premium_value'] = puts['lastPrice'] * puts['volume']

            total_calls = calls['premium_value'].sum()
            total_puts = puts['premium_value'].sum()
            data.append({'expiration': exp, 'net_premium': total_calls - total_puts})

        df = pd.DataFrame(data)
        df['expiration'] = pd.to_datetime(df['expiration'])
        df.sort_values('expiration', inplace=True)
        return df

class OptionsAnalyzer(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Options Net Premium Analyzer")
        self.setGeometry(100, 100, 1000, 700)

        # Set dark theme
        self.setStyleSheet("""
            QMainWindow {
                background-color: #2b2b2b;
                color: #ffffff;
            }
            QLabel {
                color: #ffffff;
                font-size: 12px;
            }
            QLineEdit {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #606060;
                padding: 5px;
                font-size: 12px;
            }
            QPushButton {
                background-color: #404040;
                color: #ffffff;
                border: 1px solid #606060;
                padding: 8px 16px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #505050;
            }
            QPushButton:pressed {
                background-color: #303030;
            }
        """)

        self.init_ui()

    def init_ui(self):
        """Initialize the user interface"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # Main layout
        main_layout = QVBoxLayout(central_widget)

        # Input section
        input_layout = QHBoxLayout()

        self.ticker_label = QLabel("Ticker Symbol:")
        self.ticker_input = QLineEdit("AAPL")
        self.ticker_input.setMaximumWidth(100)

        self.fetch_button = QPushButton("Fetch Data")
        self.fetch_button.clicked.connect(self.fetch_data)

        input_layout.addWidget(self.ticker_label)
        input_layout.addWidget(self.ticker_input)
        input_layout.addWidget(self.fetch_button)
        input_layout.addStretch()

        main_layout.addLayout(input_layout)

        # Chart section
        self.chart_widget = pg.PlotWidget()
        self.chart_widget.setBackground('#2b2b2b')
        self.chart_widget.setLabel('left', 'Net Premium (Millions USD)', color='white', size='12pt')
        self.chart_widget.setLabel('bottom', 'Expiration Date', color='white', size='12pt')
        self.chart_widget.getAxis('left').setPen(color='white')
        self.chart_widget.getAxis('bottom').setPen(color='white')
        self.chart_widget.getAxis('left').setTextPen(color='white')
        self.chart_widget.getAxis('bottom').setTextPen(color='white')

        main_layout.addWidget(self.chart_widget)

        # Status label
        self.status_label = QLabel("Ready to fetch data...")
        main_layout.addWidget(self.status_label)

    def fetch_data(self):
        """Start fetching data in a separate thread"""
        ticker = self.ticker_input.text().strip().upper()
        if not ticker:
            QMessageBox.warning(self, "Warning", "Please enter a ticker symbol")
            return

        self.fetch_button.setEnabled(False)
        self.status_label.setText(f"Fetching data for {ticker}...")

        # Start data fetching thread
        self.data_thread = DataFetcher(ticker)
        self.data_thread.data_ready.connect(self.update_chart)
        self.data_thread.error_occurred.connect(self.handle_error)
        self.data_thread.start()

    def update_chart(self, df):
        """Update the chart with new data"""
        self.fetch_button.setEnabled(True)

        if df.empty:
            self.status_label.setText("No data available for this ticker")
            return

        # Clear previous plot
        self.chart_widget.clear()

        # Convert dates to timestamps for plotting
        timestamps = [pd.Timestamp(date).timestamp() for date in df['expiration']]
        net_premiums = df['net_premium'] / 1e6  # Convert to millions

        # Create bar chart
        width = (timestamps[1] - timestamps[0]) * 0.6 if len(timestamps) > 1 else 86400  # 1 day width if single bar

        # Create bargraph item
        bargraph = pg.BarGraphItem(
            x=timestamps,
            height=net_premiums,
            width=width,
            brush='lightblue',
            pen='white'
        )

        self.chart_widget.addItem(bargraph)

        # Set title
        ticker = self.ticker_input.text().strip().upper()
        self.chart_widget.setTitle(f'Net Option Premiums for {ticker}', color='white', size='14pt')

        # Format x-axis to show dates
        axis = self.chart_widget.getAxis('bottom')
        axis.setTicks([[(ts, pd.Timestamp(ts, unit='s').strftime('%Y-%m-%d'))
                       for ts in timestamps[::max(1, len(timestamps)//10)]]])

        self.status_label.setText(f"Data loaded successfully for {ticker} ({len(df)} expirations)")

    def handle_error(self, error_message):
        """Handle errors from data fetching"""
        self.fetch_button.setEnabled(True)
        self.status_label.setText("Error occurred while fetching data")
        QMessageBox.critical(self, "Error", f"Failed to fetch data:\n{error_message}")

def main():
    app = QApplication(sys.argv)

    # Set application style for dark theme
    app.setStyle('Fusion')

    window = OptionsAnalyzer()
    window.show()

    sys.exit(app.exec())

if __name__ == '__main__':
    main()